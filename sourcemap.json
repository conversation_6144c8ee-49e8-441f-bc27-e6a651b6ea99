{"name": "vencord", "className": "DataModel", "filePaths": ["default.project.json"], "children": [{"name": "ReplicatedStorage", "className": "ReplicatedStorage", "children": [{"name": "Shared", "className": "Folder", "children": [{"name": "Utilities", "className": "ModuleScript", "filePaths": ["src/shared\\Utilities.luau"]}, {"name": "Sprint", "className": "ModuleScript", "filePaths": ["src/shared\\Sprint.luau"]}]}]}, {"name": "ServerScriptService", "className": "ServerScriptService", "children": [{"name": "Server", "className": "<PERSON><PERSON><PERSON>", "filePaths": ["src/server\\init.server.luau"]}]}, {"name": "StarterPlayer", "className": "StarterPlayer", "children": [{"name": "StarterPlayerScripts", "className": "StarterPlayerScripts", "children": [{"name": "Client", "className": "LocalScript", "filePaths": ["src/client\\init.client.luau"]}]}]}]}