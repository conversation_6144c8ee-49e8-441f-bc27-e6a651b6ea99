local Players = game:GetService("Players")
local UserInputService = game:GetService("UserInputService")

local player = Players.LocalPlayer

-- Sprint configuration
local NORMAL_WALKSPEED = 16  -- De<PERSON>ult <PERSON>lo<PERSON> walkspeed
local SPRINT_WALKSPEED = 24  -- Sprint speed (50% faster)

-- State tracking
local isSprinting = false
local currentHumanoid = nil

-- Function to get the current humanoid
local function getCurrentHumanoid()
	if player.Character and player.Character:Find<PERSON><PERSON><PERSON><PERSON>hil<PERSON>("Humanoid") then
		return player.Character.Humanoid
	end
	return nil
end

-- Function to start sprinting
local function startSprint()
	if not isSprinting then
		isSprinting = true
		currentHumanoid = getCurrentHumanoid()
		if currentHumanoid then
			currentHumanoid.WalkSpeed = SPRINT_WALKSPEED
		end
	end
end

-- Function to stop sprinting
local function stopSprint()
	if isSprinting then
		isSprinting = false
		currentHumanoid = getCurrentHumanoid()
		if currentHumanoid then
			currentHumanoid.WalkSpeed = NORMAL_WALKSPEED
		end
	end
end

-- Handle input events
local function onInputBegan(input, gameProcessed)
	-- Don't process input if the game is handling it (like typing in chat)
	if gameProcessed then return end

	-- Check if Shift key is pressed
	if input.KeyCode == Enum.KeyCode.LeftShift or input.KeyCode == Enum.KeyCode.RightShift then
		startSprint()
	end
end

local function onInputEnded(input, gameProcessed)
	-- Don't process input if the game is handling it
	if gameProcessed then return end

	-- Check if Shift key is released
	if input.KeyCode == Enum.KeyCode.LeftShift or input.KeyCode == Enum.KeyCode.RightShift then
		stopSprint()
	end
end

-- Handle character respawning
local function onCharacterAdded(character)
	-- Reset sprint state when character respawns
	isSprinting = false

	-- Wait for humanoid to be added
	local humanoid = character:WaitForChild("Humanoid")
	currentHumanoid = humanoid

	-- Set normal walkspeed
	humanoid.WalkSpeed = NORMAL_WALKSPEED
end

-- Connect events
UserInputService.InputBegan:Connect(onInputBegan)
UserInputService.InputEnded:Connect(onInputEnded)

-- Handle current character if it exists
if player.Character then
	onCharacterAdded(player.Character)
end

-- Handle future character spawns
player.CharacterAdded:Connect(onCharacterAdded)

print("Sprint system loaded! Hold Shift to sprint.")