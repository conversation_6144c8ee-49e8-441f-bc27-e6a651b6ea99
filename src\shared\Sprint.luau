local Players = game:GetService("Players")
local UserInputService = game:GetService("UserInputService")

local Sprint = {}

local NORMAL_WALKSPEED = 16
local SPRINT_WALKSPEED = 24

local player = Players.LocalPlayer
local isSprinting = false
local currentHumanoid = nil

local function getCurrentHumanoid()
	if player.Character and player.Character:Find<PERSON><PERSON><PERSON><PERSON>hil<PERSON>("Humanoid") then
		return player.Character.Humanoid
	end
	return nil
end

local function startSprint()
	if isSprinting then
		return
	end
	
	isSprinting = true
	currentHumanoid = getCurrentHumanoid()
	if currentHumanoid then
		currentHumanoid.WalkSpeed = SPRINT_WALKSPEED
	end
end

local function stopSprint()
	if not isSprinting then
		return
	end
	
	isSprinting = false
	currentHumanoid = getCurrentHumanoid()
	if currentHumanoid then
		currentHumanoid.WalkSpeed = NORMAL_WALKSPEED
	end
end

local function onInputBegan(input: InputObject, gameProcessed: boolean)
	if gameProcessed then
		return
	end
	
	if input.KeyCode == Enum.KeyCode.LeftShift or input.KeyCode == Enum.KeyCode.RightShift then
		startSprint()
	end
end

local function onInputEnded(input: InputObject, gameProcessed: boolean)
	if gameProcessed then
		return
	end
	
	if input.KeyCode == Enum.KeyCode.LeftShift or input.KeyCode == Enum.KeyCode.RightShift then
		stopSprint()
	end
end

local function onCharacterAdded(character: Model)
	isSprinting = false
	
	local humanoid = character:WaitForChild("Humanoid")
	currentHumanoid = humanoid
	humanoid.WalkSpeed = NORMAL_WALKSPEED
end

function Sprint.init()
	UserInputService.InputBegan:Connect(onInputBegan)
	UserInputService.InputEnded:Connect(onInputEnded)
	
	if player.Character then
		onCharacterAdded(player.Character)
	end
	
	player.CharacterAdded:Connect(onCharacterAdded)
end

return Sprint
